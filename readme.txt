﻿=== consentmanager <PERSON><PERSON> for <PERSON><PERSON> Consent (Google Consent Mode and GDPR compliant Cookie Notice) ===
Contributors: consentmanager
Donate link: no donate-link provided
Tags: cookie banner, GDPR, google consent mode, IAB TCF
Requires at least: 3.0
Tested up to: 6.8
Stable tag: 3.1.0
Requires PHP: 5.4
License: GPLv2
License URI: https://wordpress.org/about/gpl/

The consentmanager Cookie Banner and Cookie Notice allows you to easily collect cookie consent from your website visitors, ensuring GDPR compliance.

== Description ==


The [consentmanager Cookie Banner](https://consentmanager.net/) provides a comprehensive, effortlessly integrated Consent Management Platform (CMP) plugin tailored for WordPress. **Create your consent banner in minutes** and protect your website(s) from legal fines, designed by privacy experts for companies of all sizes and industries. It supports global compliance with key privacy laws such as **GDPR (EU), CCPA (US) , FADP (Switzerland), PIPEDA (Canada)** and privacy frameworks such as the **IAB Transparency and Consent Framework v2.2** and the **Global Privacy Platform (GPP)**. In addition, consentmanager keeps pace with a wide range of internet-related requirements that are critical for professionals in marketing, advertising, analytics, and related fields. Users can achieve full compliance with **Google Consent Mode v2** and other Google services, **including Google AdSense, Google Ad Manager and Google AdMob**, and **safeguard your monetisation efforts** with the **Google-certified CMP** from consentmanager. The consentmanager WordPress plugin gives you access to an advanced CMP framework with seamless compatibility with over 2500 tools and language support for over 30 options.

Designed for plug and play simplicity, consentmanager users can go much further with the use of **A/B testing** functionality, **advanced machine learning** and the ability to use our **reporting tools of over 30 metrics**. With the ability for customisation, our platform is tailored to the specific needs of your site. It has a **built-in cookie crawler** and offers **extensive customization options to match your site's aesthetic, enhancing the user experience without compromising on compliance.

**How does it work?**

**Our CMP solution is very simple to integrate:**

1. Simply login to your consentmanager.net account
2. Setup the cookie banner for your website(s)
3. Copy your CMP-ID which you can find in your consentmanager dashboard, and paste it into the consentmanager wordpress plugin

And you’re done!

**Our platform will automatically start gathering cookie consent notices from your visitors.** As soon as the code is in your website, advertisers will have access to the consent data via the open source API defined by the IAB TCF.

In addition you will get **detailed reports** which show you how your visitors are **behaving with the consent layer**, how many consents you get and **how you can optimize your strategy in order to obtain higher rates of consent.**

**Features**

* Fully customisable: **Tailor your cookie banner** to fit seamlessly into your website's aesthetic.
* A **Google certified CMP** and fully compliant with **Google Consent Mode v2**.
* **Integrated Cookie Crawler:** Automatically detect and manage cookies and trackers on your site.
* **Multi-language support:** Reach a global audience with support for **over 30 languages.**
* Comprehensive compliance: Ensures compliance with **GDPR, CCPA, FADP, PIPEDA and other key regulations.**
* **Easy integration:** Simply copy and paste your unique CMP ID into the consentmanager WordPress plugin.
* Extensive **tool compatibility**: Works seamlessly with over **2500 tools and platforms**.
* IAB TCF Framework participation: A proud member and **co-author of the IAB TCF  & GPP Framework**, our CMP ensures **compliance with industry standards**.

**Quick Facts**

**To help you understand the scope and effectiveness of the consentmanager WordPress plugin, consider these quick facts:**

* consentmanager's Cookie Notice has helped more than 25,000 websites become compliant
* Compatible with major solutions like **Google Analytics, Facebook, Hubspot, and many more.**
* Exclusively utilizes **European servers for data storage**, ensuring data sovereignty and compliance with **"Schrems II"** requirements.
* Supports website integration, AMP-sites, and **mobile apps (Android/iOS)**, with optional **age-verification mechanisms**.

**Tools offered by consentmanager:**

* Consent/Cookie Banner Generator
* Privacy policy text generator
* Whistleblower Software
* Mobile SDK Integration
* Data Subject Rights Management
* Privacy-friendly Website Analytics

**Support and Updates**

The consentmanager team is dedicated to providing continuous updates and support, ensuring your website remains compliant with evolving privacy regulations. For assistance or more details, visit our support center or contact us directly.

* [Support Centre](https://help.consentmanager.net/)
* [Contact us](https://www.consentmanager.net/contact/)
* [E-Mail us](mailto:<EMAIL>)

**About consentmanager**

With over a decade of experience in the adtech industry and a commitment to data privacy and compliance, consentmanager has established itself as a trusted leader in consent management. Our Cookie Banner technology empowers businesses across the globe to manage user consent effectively, fostering trust and ensuring compliance with international privacy laws.





== Installation ==


Simple installation. Install our plugin in your Wordpress, login to your account & get your CMP Code-ID at [https://www.consentmanager.net](https://www.consentmanager.net) and insert the Code-ID in the plugin settings. That’s all!


== Frequently Asked Questions ==


= Do I need a CMP? =

Short answer: Probably yes. Long answer: If your company is based in the EEA (European Economic Area) or if you are dealing with customers/visitors from this area and show them advertising, it is very likely that you will collect and/or process personal data such as IP-addresses. Therefore, according to GDPR, you need to make sure that the visitor is informed and you need to ask the user for consent. In order to do this you will need a CMP.

= When does this become necessary? =

GDPR is "active" since 25. of May 2018. From this day on you will probably need a CMP.

= How do I obtain consent from my visitors? =

By integrating our CMP into your website ;-) Our CMP will display a message to visitors and ask them to give consent. We will then store this choice and make it available to your advertisers and other vendors (tools) so that they know if/how they can work with personal data.

= Will the CMP block advertisers from my page do not have consent? =

Yes, our CMP offers a solution to block advertisers/codes that do not have consent from the visitor.

= Is it complicated? =

No! In the simplest case you only have to integrate a code into your website - that's all!

= Your question is missing here? =

Any question that you think is missing here? Get in touch with us and tell us your questions!


== Screenshots ==


1. Cookie Banner Example 1

2. Cookie Banner Example 2

3. Cookie Banner Example 3

4. Cookie Banner Example 4


== Changelog ==


1.0.1 - fixing a small bug while saving the ID

1.0.2 - fixing some style

1.0.3 - adding some screenshots

1.0.4 - small update

1.1.0 - adding CCPA compliance

1.1.3 - small validation updates, tested up to WP RC 5.7

2.0 - adding additional features: automatic serverside blocking + automatic clientside blocking

2.0.1 - disable CMP WP backend

2.0.2 - optional enable/disable CMP for logged in WP admins

2.0.3 - smaller bug fixes

2.0.4 - updated blocking codes | cleaned up coding standards/identations | cleaned up class names | removed debug code | renamed functions with prefixes | escaped posts properly | removed logic of saving local file | temporary removed serverside blocking mode until found performance solution | sanitized & escaped input codes | native enqueue functions for scripts | cleanup code styles | fix for adding data attributes to autoblocking script | autoblocking script higher in head

2.0.5 - added host/cdn fields [optional]

2.0.6 - fix for semi-automatic blocking code

2.0.7 - bug fix: remove protocol for semiautomatic blocking

3.0 - ready for WordPress 6.0

3.0.1 - Replaced wrong screenshot

3.0.2 - Ready for WP 6.1

3.0.3 - Remove old hint to pubvendors.json

3.0.4 - Fix spelling mistake

3.0.5 - Ready for WP 6.2

3.0.6 - Ready for WP 6.5

3.0.7 - Ready for WP 6.6

3.0.8 - Minor fixes

3.1.0 - Ready for WP 6.8

== Upgrade Notice ==
upgrade now
